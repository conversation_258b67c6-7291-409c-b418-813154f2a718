# frozen_string_literal: true

# Initialize consultation system notifications
Rails.application.config.after_initialize do
  # Only run if the database and tables exist
  begin
    next unless ActiveRecord::Base.connection.table_exists?('notifications')

    # Create consultation notification types if they don't exist
    ConsultationNotificationService::NOTIFICATION_TYPES.each do |key, name|
      next if Notification.find_by(name: name)

      category = case key
                 when :request_submitted, :request_approved, :request_declined, :consultation_completed
                   'Consultation'
                 when :consultation_reminder
                   'Calendar'
                 else
                   'Other'
                 end

      Notification.create!(
        name: name,
        category: category,
        subject: name,
        main_link: '/consultations'
      )
    end
  rescue ActiveRecord::NoDatabaseError, PG::ConnectionBad
    # Database doesn't exist yet, skip initialization
    Rails.logger.info "Skipping consultation notifications initialization - database not ready"
  end

  # Note: Consultation reminder job is scheduled via periodic_jobs.rb
end
